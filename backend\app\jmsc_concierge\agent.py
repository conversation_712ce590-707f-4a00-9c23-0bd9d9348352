from .recipe_agent.agent import recipe_agent
from google.adk.tools import agent_tool
from google.adk.tools import ToolContext
from google.adk.agents import Agent
import logging
from chromadb.utils import embedding_functions
import json
import chromadb
import os
import sys
__import__('pysqlite3')
sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')


# --- Load Configuration & Initialize Client ONCE ---
# This code runs only once when the agent service starts, making it very efficient.
logging.info("Initializing ChromaDB client and embedding function...")
CHROMA_HOST = os.getenv("CHROMA_HOST", "chroma")
CHROMA_PORT = os.getenv("CHROMA_PORT", 8000)
CHROMA_COLLECTION = os.getenv("CHROMA_COLLECTION", "products")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2")

client = chromadb.HttpClient(host=CHROMA_HOST, port=CHROMA_PORT)
sentence_transformer_ef = embedding_functions.SentenceTransformerEmbeddingFunction(model_name=EMBEDDING_MODEL)
collection = client.get_or_create_collection(name=CHROMA_COLLECTION, embedding_function=sentence_transformer_ef)
logging.info("ChromaDB client initialized successfully.")

recipe_agent_tool = agent_tool.AgentTool(agent=recipe_agent)


def search_liquor_store_products(queries: list[str], tool_context: ToolContext) -> list[dict]:
    """
    Searches for products available in the liquor store based on a list of user search terms.
    This is the primary tool to use when a user asks for any kind of product recommendation.

    Args:
        queries: A list of search terms describing what the user is looking for. 
                 For example: ["a smoky Islay scotch", "something cheap for a margarita"]

    Returns:
        A list of dictionary objects, where each dictionary represents a unique product found. 
        Each product dictionary contains details like name, brand, price, and description.
    """
    print("----- Tool: search_liquor_store_products -----")
    print(f"Received queries: {queries}")

    # Use a dictionary to store found items, using the item's ID as the key.
    # This automatically handles duplicates if different queries return the same product.
    found_items_by_id = {}

    for query in queries:
        print(query, "query")
        try:
            results = collection.query(
                query_texts=[query],
                n_results=10,  # Fetch top 3 results for each query
                where={"visibility": "visible"}  # CRUCIAL: Only find products that are in stock
            )
            # print(f"results debug: {results}")

            # Process the results from this query
            if results and results['ids'] and results['ids'][0]:
                for i, item_id in enumerate(results['ids'][0]):
                    if item_id not in found_items_by_id:
                        metadata = results['metadatas'][0][i]

                        # Parse JSON strings in metadata
                        def safe_json_parse(json_str):
                            if isinstance(json_str, str):
                                try:
                                    return json.loads(json_str)
                                except (json.JSONDecoderError, ValueError):
                                    return json_str
                            return json_str

                        # Parse nested JSON Fields
                        size_data = safe_json_parse(metadata.get('size', '{}'))
                        images_data = safe_json_parse(metadata.get('images', '[]'))
                        product_insight = safe_json_parse(metadata.get('productInsight', '{}'))
                        pack_size_detail = safe_json_parse(metadata.get('packSizeDetail', '{}'))

                        # Format a clean product dictionary for the agent
                        product_info = {
                            "id": item_id,
                            "upc": item_id,
                            "item_name": metadata.get('db_item_name'),
                            "name": metadata.get('name'),
                            "brandName": metadata.get('brandName'),
                            "sale_price": metadata.get('db_sale_price'),
                            "size": size_data.get('sizeValue') if isinstance(size_data, dict) else None,
                            "main_category": metadata.get('db_main_category'),
                            "description": metadata.get('description'),
                            "images": images_data if isinstance(images_data, list) else [],
                            "productInsight": product_insight if isinstance(product_insight, dict) else {},
                            "pairingInfo": metadata.get('pairingInfo'),
                            "alcoholeContent": product_insight.get('alcoholContent') if isinstance(product_insight, dict) else None,
                            "rating": product_insight.get('rating') if isinstance(product_insight, dict) else None,
                            "packSizeDetail": pack_size_detail if isinstance(pack_size_detail, dict) else {}
                        }
                        # Ensure all keys are present, defaulting to None if missing
                        for key in ["id", "upc", "item_name", "name", "brandName", "sale_price", "size", "main_category", "description", "images", "productInsight", "pairingInfo", "alcoholeContent", "rating", "packSizeDetail"]:
                            product_info.setdefault(key, None)
                        # print(f"metadata debug: {metadata}")
                        found_items_by_id[item_id] = product_info
        except Exception as e:
            print(f"Error during ChromaDB query for '{query}': {e}")
            # Continue to the next query even if one fails
            continue

    # Convert the dictionary of unique items back into a list
    final_items_list = list(found_items_by_id.values())

    print(f"Found {len(final_items_list)} unique items in total.")
    # if final_items_list:
    #     # print(f"Sample item structure: {final_items_list[0]}")
    #     # print(
    #     #     f"Sample item keys: {final_items_list[0].keys() if isinstance(final_items_list[0], dict) else 'Not a dict'}")

    result = {
        "status": "success",
        "products": final_items_list
    }

    # tool_context.state["products_data"] = result
    # print(f"\n {final_items_list} .")

    print("---------------------------------------------")
    return result


def display_products_by_upcs(upcs: list[str], tool_context: ToolContext) -> dict:
    """
    Connects to a ChromaDB instance and retrieves multiple products by a list of UPCs in a single batch.

    Args:
        host (str): The hostname or IP address of the ChromaDB server.
        port (str): The port number for the ChromaDB server.
        collection_name (str): The name of the collection to query.
        upcs (list[str]): A list of UPCs (IDs) of the products to retrieve.

    Returns:
        dict: The product data retrieved from the collection, including metadatas.
              Returns None if the upcs list is empty or not provided.
    """
    print("----- Tool: display_products_by_upcs -----")
    if not upcs:
        print("Error: Please provide a list of UPCs.")
        return None

    print("UPC", upcs)

    try:
        # --- Connect and fetch ---
        client = chromadb.HttpClient(host=CHROMA_HOST, port=CHROMA_PORT)
        collection = client.get_collection(name=CHROMA_COLLECTION)

        # --- Get the items by their IDs (which are the UPCs) in a single batch ---
        raw_data = collection.get(
            ids=upcs,
            include=["metadatas", "documents"]
        )

        # Format the data like search_liquor_store_products does
        formatted_products = []
        if raw_data and raw_data.get('ids'):
            for i, item_id in enumerate(raw_data['ids']):
                metadata = raw_data['metadatas'][i]

                # Parse JSON strings in metadata
                def safe_json_parse(json_str):
                    if isinstance(json_str, str):
                        try:
                            return json.loads(json_str)
                        except (json.JSONDecodeError, ValueError):
                            return json_str
                    return json_str

                # Parse nested JSON Fields
                size_data = safe_json_parse(metadata.get('size', '{}'))
                images_data = safe_json_parse(metadata.get('images', '[]'))
                product_insight = safe_json_parse(metadata.get('productInsight', '{}'))
                pack_size_detail = safe_json_parse(metadata.get('packSizeDetail', '{}'))

                # Format a clean product dictionary
                product_info = {
                    "id": item_id,
                    "upc": item_id,
                    "item_name": metadata.get('db_item_name'),
                    "name": metadata.get('name'),
                    "brandName": metadata.get('brandName'),
                    "sale_price": metadata.get('db_sale_price'),
                    "size": size_data.get('sizeValue') if isinstance(size_data, dict) else None,
                    "main_category": metadata.get('db_main_category'),
                    "description": metadata.get('description'),
                    "images": images_data if isinstance(images_data, list) else [],
                    "productInsight": product_insight if isinstance(product_insight, dict) else {},
                    "pairingInfo": metadata.get('pairingInfo'),
                    "alcoholContent": product_insight.get('alcoholContent') if isinstance(product_insight, dict) else None,
                    "rating": product_insight.get('rating') if isinstance(product_insight, dict) else None,
                    "packSizeDetail": pack_size_detail if isinstance(pack_size_detail, dict) else {}
                }
                formatted_products.append(product_info)
                # Ensure all keys are present, defaulting to None if missing
                for key in ["id", "upc", "item_name", "name", "brandName", "sale_price", "size", "main_category", "description", "images", "productInsight", "pairingInfo", "alcoholeContent", "rating", "packSizeDetail"]:
                    product_info.setdefault(key, None)
                    
            result = {
                "status": "success",
                "products": formatted_products
            }
        tool_context.state["products_data"] = result["products"]

        # products_data = tool_context.state["products_data"]
        # products_list = products_data["products"]  # This is the actual list

        # print("products_data debug", result)
        return result
    except Exception as e:
        print(f"Error retrieving products by UPCs: {e}")
        return {"status": "error", "message": str(e)}


instruction = """
You are a shopper's concierge at a liquor store with millions of items. Your goal is to assist users in finding the perfect products while providing detailed and persuasive information to motivate them to make a purchase. Follow the steps below:

Receiving Search Requests:
When you receive a search request from a user, generate 2 distinct queries related to the search. These queries will be used to find relevant products on vector db.but never give this querys to user this is for our internal process direct use tool and fetch products using this querys.

Searching for Products:
Pass the list of queries tool search_liquor_store_products to find matching items in the liquor store's database. this is your internal process never told customber about this.

3.  **Crafting Human-Friendly Responses:** Once you have a list of products, respond to the customer with a concise, natural, and helpful answer. Highlight key details about the products in a way that sounds like a genuine, enthusiastic human expert.

Displaying Products for UI (JSON Response):
For the products you display, call display_products_by_upcs with the corresponding `id` to gather detailed information about the products. This call will generate a UI view in the form of product cards for the user. This response is meant for displaying the product information in a user-friendly format.

also if user ask for recipe for coktail then you can use recipe_agent_tool and after you can ask user if they want to search recipe ingridiants.
Important: Never inform the customer that you're using display_products_by_upcs or provide them with the details of the call.Also never tail customber to querys and any internal functionality
You are one human who can give store information to customers for upselling products. and your store is located in the USA. Also, every time when you fetch the products, you should display relevant products on the UI.

"""

root_agent = Agent(
    model='gemini-2.0-flash-live-001',
    name='root_agent',
    description=(
        'Shop agent for an liquor store'
    ),
    instruction=instruction,
    tools=[
        display_products_by_upcs,
        search_liquor_store_products,
        recipe_agent_tool,
    ],
)
